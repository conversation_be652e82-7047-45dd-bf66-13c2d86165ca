curl 'https://signup.snowflake.com/api/v1/telemetry_beacon' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -b 'signupsession=eyJleHBlcmltZW50U2VnbWVudHMiOnsiU0FOSVRZX0FBX1RFU1QiOjF9fQ==; signupsession.sig=QvGyu2U7LKU5ePq1KDZdVYsttXE' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=4, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' \
  --data-raw '{"body":[{"component":"SurveyCard","location":["SignupPage"],"interaction":true,"flags":{},"event":"post_signup_acquisition_source_survey","type":"ui_click","data":{"question":"Select your preferred language(s) to work in","version":"v3","selection":{"No coding language experience":true,"Java":false,"Python":false,"Scala":false,"SQL":false,"Other":false,"otherUserInput":""},"questionOrder":["What will you use Snowflake for?","Select your preferred language(s) to work in"],"optionOrder":["Java","Python","Scala","SQL","Other","No coding language experience"],"formId":"f7469113-a110-44a5-a7ca-70c6d34b52b2","timestamp":"2025-07-31T15:09:05.822Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/?utm_cta=trial-en-www-homepage-top-right-nav-ss-evg&amp;_ga=2.74406678.*********.1657561304-1006975775.1656432605&amp;_gac=1.*********.1656541671.Cj0KCQjw8O-VBhCpARIsACMvVLPE7vSFoPt6gqlowxPDlHT6waZ2_Kd3-4926XLVs0QvlzvTvIKg7pgaAqd2EALw_wcB#","mutinyExperienceCount":0,"mutinyExperiences":[]}},{"component":"SignupCard","location":["SignupPage"],"interaction":true,"flags":{},"event":"signup_form_one_complete","type":"ui_click","data":{"firstName":"dhr","lastName":"sehe","email":"<EMAIL>","country":"Japan","optOutEmailAgreement":false,"formId":"f7469113-a110-44a5-a7ca-70c6d34b52b2","signupReason":"Other","formOneCompletionTime":0.12199997901916504,"timestamp":"2025-07-31T15:09:11.878Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/?utm_cta=trial-en-www-homepage-top-right-nav-ss-evg&amp;_ga=2.74406678.*********.1657561304-1006975775.1656432605&amp;_gac=1.*********.1656541671.Cj0KCQjw8O-VBhCpARIsACMvVLPE7vSFoPt6gqlowxPDlHT6waZ2_Kd3-4926XLVs0QvlzvTvIKg7pgaAqd2EALw_wcB#","mutinyExperienceCount":0,"mutinyExperiences":[]}},{"component":"SignupCard","location":["SignupPage"],"interaction":true,"flags":{},"event":"signup_form_two_complete","type":"ui_click","data":{"firstName":"dhr","lastName":"sehe","email":"<EMAIL>","company":"rhse","phoneNumber":"*********","role":"CEO","edition":"Enterprise","cloud":"gcp","region":"me-central2","country":"Japan","recaptchaToken":"REDACTED","signupUrl":"https://signup.snowflake.com/?utm_cta=trial-en-www-homepage-top-right-nav-ss-evg&amp;_ga=2.74406678.*********.1657561304-1006975775.1656432605&amp;_gac=1.*********.1656541671.Cj0KCQjw8O-VBhCpARIsACMvVLPE7vSFoPt6gqlowxPDlHT6waZ2_Kd3-4926XLVs0QvlzvTvIKg7pgaAqd2EALw_wcB#","formId":"f7469113-a110-44a5-a7ca-70c6d34b52b2","formTwoCompletionTime":0.661,"referrerUrl":"","timestamp":"2025-07-31T15:09:17.789Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/?utm_cta=trial-en-www-homepage-top-right-nav-ss-evg&amp;_ga=2.74406678.*********.1657561304-1006975775.1656432605&amp;_gac=1.*********.1656541671.Cj0KCQjw8O-VBhCpARIsACMvVLPE7vSFoPt6gqlowxPDlHT6waZ2_Kd3-4926XLVs0QvlzvTvIKg7pgaAqd2EALw_wcB#","mutinyExperienceCount":0,"mutinyExperiences":[]}},{"component":"SignupPage","location":["SignupPage"],"interaction":false,"flags":{},"event":"signup_suppressed","type":"ui_response_success","data":{"thankYouResponseDuration":0.8399999141693115,"suppressionReason":"SUPPRESSED_EMAIL","formId":"f7469113-a110-44a5-a7ca-70c6d34b52b2","timestamp":"2025-07-31T15:09:18.629Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/?utm_cta=trial-en-www-homepage-top-right-nav-ss-evg&amp;_ga=2.74406678.*********.1657561304-1006975775.1656432605&amp;_gac=1.*********.1656541671.Cj0KCQjw8O-VBhCpARIsACMvVLPE7vSFoPt6gqlowxPDlHT6waZ2_Kd3-4926XLVs0QvlzvTvIKg7pgaAqd2EALw_wcB#","mutinyExperienceCount":0,"mutinyExperiences":[]}},{"component":"SignupPage","location":["SignupPage"],"interaction":false,"flags":{},"event":"signup_success","type":"ui_response_success","data":{"thankYouResponseDuration":0.8399999141693115,"resultSuccess":false,"formId":"f7469113-a110-44a5-a7ca-70c6d34b52b2","timestamp":"2025-07-31T15:09:18.629Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/?utm_cta=trial-en-www-homepage-top-right-nav-ss-evg&amp;_ga=2.74406678.*********.1657561304-1006975775.1656432605&amp;_gac=1.*********.1656541671.Cj0KCQjw8O-VBhCpARIsACMvVLPE7vSFoPt6gqlowxPDlHT6waZ2_Kd3-4926XLVs0QvlzvTvIKg7pgaAqd2EALw_wcB#","mutinyExperienceCount":0,"mutinyExperiences":[]}},{"component":"SignupPage","location":["SignupPage"],"interaction":false,"flags":{},"event":"signup_compute_default_region","type":"ui_response_success","data":{"aws":"ap-northeast-3","azure":"japaneast","gcp":"me-central2","formId":"f7469113-a110-44a5-a7ca-70c6d34b52b2","timestamp":"2025-07-31T15:09:29.320Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/?utm_cta=trial-en-www-homepage-top-right-nav-ss-evg&amp;_ga=2.74406678.*********.1657561304-1006975775.1656432605&amp;_gac=1.*********.1656541671.Cj0KCQjw8O-VBhCpARIsACMvVLPE7vSFoPt6gqlowxPDlHT6waZ2_Kd3-4926XLVs0QvlzvTvIKg7pgaAqd2EALw_wcB#","mutinyExperienceCount":0,"mutinyExperiences":[]}},{"component":"SignupCard","location":["SignupPage"],"interaction":true,"flags":{},"event":"signup_form_one_complete","type":"ui_click","data":{"firstName":"dhr","lastName":"sehe","email":"<EMAIL>","country":"Japan","optOutEmailAgreement":false,"formId":"f7469113-a110-44a5-a7ca-70c6d34b52b2","signupReason":"Other","formOneCompletionTime":2.7649998664855957,"timestamp":"2025-07-31T15:09:31.632Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/?utm_cta=trial-en-www-homepage-top-right-nav-ss-evg&amp;_ga=2.74406678.*********.1657561304-1006975775.1656432605&amp;_gac=1.*********.1656541671.Cj0KCQjw8O-VBhCpARIsACMvVLPE7vSFoPt6gqlowxPDlHT6waZ2_Kd3-4926XLVs0QvlzvTvIKg7pgaAqd2EALw_wcB#","mutinyExperienceCount":0,"mutinyExperiences":[]}}],"headers":{"signup-flow-type":"Default","x-mutiny-experience-count":"0"}}' ;
curl 'https://www.google.com/recaptcha/api2/userverify?k=6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/x-www-form-urlencoded;charset=UTF-8' \
  -b '__Secure-3PAPISID=9VNYYMeISeXkff0Q/Am6Sp8MBujo3jUQ-d; __Secure-3PSID=g.a000zQiGQcQeIwAt-jCi94J1TR9reXyY0GcyOjxrc5xs2IMzZEj4Zh-4iSo-yLvPsjFxyCRjZwACgYKAQ4SARYSFQHGX2MisTZ83uR2iukotk1DOkaz6xoVAUF8yKq6eit5GLCUgBVFlzeAzaLy0076; __Secure-3PSIDTS=sidts-CjEB5H03PwGmOelFxvf4EmSvg7YjsN_0byomVkIlqeEQv-yWKL_U_OF1QOpj8wIs3RVdEAA; NID=525=a6RBRuMmIla-YpEi8xfmjWF5f_X_ychJm4DpGrLZ6hvLWcSW-8o3LLwici419xWmoBM0-XasafCnGCMTzn_ZDjrw3DqOcMyYr7o5HYiSClTUhpaA-JE5ANiw42vBNLwwrSJtqw9inX--M8hmf6necA3H3R_Hm4Mdah7FhPO6zbIHDlAgZbcj7r6YwWvX0jRwzZ3U_fhEg0zUc1GdGsmkcbxO98co8Wp3smQMDaKEhrLFB-hVIMtww3VF0hnvQ94YhR9DBh2rsgiNm9BxOTpI3q8U03WvdakGis3mkB1P7OJUsy04-K2tJmnm27yqLhk7CfBge8UwqTxCBv-VmOp0ebs9PDJ56RNBYoVVD_pegJ8jwIkDhBfell4MZshHKnkGXRzQLmmTmdUgtKuAhZhAxDfK4VA20_opJ0nOosIEQy-UA5RLLRcLziBIXm6L83aNeJDuJr9XrlCzGveRt3-f9Hkh6c99Lomrn04F-0sW6BJAECPlvdHFDVwfsVELlJvsklWT-oruQkueAqAWXKsGvHEBpSiW4CKEud46e8TfoKchl1LrIeDkpYSnktbKtpTLtvabp3IUfJbaysNyqCeyTtdLr3DWVK2ARi9sR_HNJNCWqMeSNkxUs5hUtndwqxe_uM1xxQrI09CKwO20-eSRRlePmDdyOREjvklRp319MMBfabKBEqzU0dyNkBx_DmOnjFN_IN6P1_DuBRCwDtFtAYp7oJyWJF2TH7iccV6OlfqB-dbLGV8aEwdLYWEVWa6mLK4z6avdiVvEI1Rj2A-OpoVv3-Hcg2jBNYbbLi9jVKd3Gtwp5mxeg7g0c624BMm-OShwUvtSY0qJLcqK0zb8CVCUqWuikZSFs6-LypUgYuZLM0bWUdDC4erxAdwWcpfIxBGqZbi6jEITgd4k7HV8GQj9-LVZvvQNBQMRdrUGGGI-IvdPaqU2wDCXVycT2pEOvp9XmcaVQj4hvcFqa7fsBTv9ObzD9leNqnUpha2d-Uzkai90ZQv_ecyPXg2H07zrSoPTZ-3JjWm8SVUzP-SDbdgrHHjRHSqx5dSCJKdBuI3kc2yj7cORLUO3SIjLAb8NgbIgWXoKxaqJvv1wQC6zUspKJ5Tt8BHcqEIUNkYfgzA4A4E; __Secure-3PSIDCC=AKEyXzXCb2oddGGiYOCVlJwimAVUohhYaNqfB48Jun6HmBXJlgGUXjDo5Yr98tQsz8-63f5tWu8' \
  -H 'origin: https://www.google.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://www.google.com/recaptcha/api2/bframe?hl=zh-CN&v=DBIsSQ0s2djD_akThoRUDeHa&k=6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'sec-fetch-storage-access: active' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' \
  --data-raw $'v=DBIsSQ0s2djD_akThoRUDeHa&c=03AFcWeA4aXgBBDqt9QwHqJ5MAYIzhGgrGNzZOBBN1mhCDvX1kw4WItRuJJCgMknXAHo11J2DHZ9fkGLVReTRI4Hl0VgWlS_Mf-akZ2s6eJqWiTkd5q-_S4QzGDXAEthz8PVrReOaa65RRIfDtpmyA21lrAjaZLttpx6RtWTLRVDhnDSAihIrHX6cR7CGqalSddcWtU1zvKfVg2zs1RAJbvA_KOgml0iztKX9C1Foxs1fffIebpqjYVvOASXL3_FqSifMz9EQPlTtZFJuWkJzoZPffRgKR21Loiv0mTRLC89hdwwgNF3RR14Sh_mtLcXZKAinecutTzfRw6wOo9OdUBalnlP_qQt3OFpH3j6gUvm5qbfd8LybQ9DHEMwM2Vy01pJsFZrQf79vCEo_xyOmeDed3ikdrj9gOKiECvxUGGV_Ag86uiag0gGCQNULiEDgb6XK3zm8rN1_lmERGWydQ7aIj5A0cfVdKL7XcwBUALD-8JH0iyF-oZQmpuFcrX83cceEohEIsWplvaoszfhh9ZObaGN46jZVZAin4q_tY-PjO3u08I1VaQzHwn9iSNeSV9YwdYB2P77r6ZFJGGv1VjHrv9TzVuBLWGEnxxFESg_ak-T1kuDJUq2208v4pas32akLrLgmafUsrmv0zjQk-YH0mE4L4m32SSzUMrPHEUECaERwxIBNuUkQ7Xl1jFnLPGaLle1mu743d5YKoTRWzQQqFKaZpo1J8YFgmYRoiqZ9Cpc2JGWb3thpOjqXZa4zAQLQs9dzvoj8XHVbqSaOok8ltp_Cznel0ZUFBLqZss7nh8rr945NiIW-oMO_3LBwvqCWmdBv1P32cAlvQIsfhK48D9EMTCfIX2rAcDOz31HwwpmN_ujSLRtyXGbkLjIdZVKkFDfjpgR03GA12jD1zaKSOljcpaNUTPUK7-P6PofPaX2RKY-U6FQL_6JpTt5SZKazGZJYAv9Jh1MB4nzqUKyOBMknzEgKBi9V7NcQmPdUqCaAGH-GBcMY5WrNCkbhqtrtFCnd5ikz-B2-rsLaWQta28M9aG9_9mXETVwqa74I6VFgfj4AjtX29BlS4-7xF4iCLnlkTR99sKauLKBrFuBKchpZoisfGqc3gAN0DG8h5NREsQODQH9kLzD4i1IFRvGuIKeeqejyR6Pit7Y_AyJej20V8EDEB23aEBdESvoro9jpcDjtMtYsBgiXUbEn8RANOM7ZNmIAuAHFRqm_Y1zEs8oN8U4FtCIswNAHAo2_bN563dxKOHwnLQiaksGpJQGplKnflKfIqrltNz-sRWYH6pvVfGlyjYZdb5I3KwUvx7uGWD30spJzM-BOzXnGpwgz_lGqyAbzrQdIs44q4HY2X5WC4ZcOX_KDqdr9JbJP8JrMRGnbfDLbmMVdAScjmqmVXvfluEw1n9NMjEmP4LHy1kUZzI5ESZfOPkPJwAHIRzT8g-tx7idE8tZB7I3XPw0VfDzBwWalkC7MV2Qsu8i3b4TP5J8_wjV4Yl_Pm3GawtNUigeYwFjqIu9ZAu6gzYZkc1xmo7aczngd4WSuGGYKDu9E5wXfCftKPuI7rOY5Ly1kLn_TmrVhHR5uhrVSdLEM7_w8ZlbejBT7uCw-8mV0iw9JOK-kUxW4sAkHXRlEHGUOwPZZOdAvCdqldg94u2ocrrGLr8RXFMchQmOjrkQRM5TAqbXddc1f4jyKqxVnwQlDiO3fKA168z4ErJXSDY_XZUl620fVNZkt5g10228PrTrg67SZj5uupFRPdFZvh6dsKvVFe7n3pRbaj1Rxk7nNX89omp-LJxWv3tw8ArDQUQH3haSY9gGYbyI2t_ukCSxTfMKgpdTvmFhrWFEYpZ0pL5YdZnJz5iXw0YAOwhRrTTUZeSpvFdBMwK23GmV3C_dBYW5sb9gq_hrx74-V-mywu3re557DEZhxXPWaklAY6MhpAOeMYFUcLE3tbRXXrfqAJwe_Of94ehKRtnEfvAnGxqERy09pnm1llYz1WWwLLOm6UxOp7NLfxVnNTRmF1O-UwoH3btcReISxzwGZoGhh6N5-BCk6h4SAVa57XhW6KWKf6GI2r-0hg8jvHblOMjWRti3JGQuAv5--9G06RV6wFvGucMFncJBWGGgj4jlcj2VwdAmLWkw5LgVjNGtQfKAaSlyLzhGl8qsOcEOfVgZj8pTAKe3K5uY9YCLa9eyl_SbaZVE9pogtNo2ITZuqZrWBjXbfj8Yqtb6kYr-CRJAzH3BO0iFp-wRcLHjqGnIJ3WQZk_p3t2TNHNSijLm_s6B3xlHp6ZIOFO9r7mSBYbCI-J17TcEYDztIU5Fqxd49QzisOe8HN9FI9aVxJ_pQfdC6t221sgLCh6Cbq38oz2vu_NwZHzLS6iJ8bEqowKVRfVIBTHDFyA4EhAr7IPPwfQDcMdWDBjmBUHQlGzEVlDi2tUntaC6_CNeSNMYiOzP0Ay2RCw4WYlpmwUikBB7PzOru4n3w-EbH42WyJMb9vFHVEmf5KnWikaXHAwQJjiFrqIRn2j-csmUlvS3HZiUjPNiw3V10UP10xlrskWmYq2OTWaphW_jZNeopVbVlM-JkXd7yoes4_DhsmsRnJ-SzDgLrCWbA_HC8tY_tCyIwT5xN6XzfzhgZfh14yjJaw8PaMOqFpz6JnLwvNBjFTk-6Vbzor31EAvaKF73RxbzwA9FcloeUrvrfQIbtk_pOwjUWPYQciik4O310drdSi_jo3kbaSd3VUOwFnK_q8DIwqKsujs_ouGB17oLHPSj_gev2bxenYDie03UjWRqt5ZJ3OFxPZpvu1yo14GUydGoTo4GBgsxSKtE0x5z335sFeTueLDFD3ieF5GVJ5h9y5T8ZvBb6a-k0vy9mgcryVkuVIQtrVC_hQX1Hs-4hmeMbEEN8CAFEfB3NQrRerX6btCMCvTHnvYVdGkzY-9L9iUJmVLHV2-H-NjHiLpffurjttsLUC4KklDm4BUMrrurgaofvqvGAtp1UNA9JbxaPX93ZC60-iBnEWaUEB7ocxrpbO6dKiJNjHrRUyjDGZA1XTpCbrEUez3vmAfi6cGqYxxn2dWJ2xJxtkjz2AFtDZHLDP-GnSn5bux6-hlAPd4ns5CJ3UDI-gJPI2KSZXDbosqdtxjfGcR3A9VFyMTc-Y7wi7pxNfExva28f-jJEfoo2Fhx_L1hY_dQeoEIeI6DrQMJ7_jh_G2iMxXB7B7nUkFJ11aIxZNONL_TMfIgOYwk0alO6Ex64AGSXydUL7Bj5s8jcqrlA_lRSTMHFVyhf6i4MF9-v_UJZUJVnlob2OpqcJlTZ4wk9XWpL5urIcg959j3uFbGiM8wKHpVTXfvBYL_COA5WDfH9fYm_fp7D6QNDxzIu0HlVLAywFHZ1orJ4yLoR2XmoI2iY8NDD5AbmI70xYfKTC2i19oKeqdNfqg7z_jRhXyxy5jdvIlbhhfdOMGZCkY-3AYWQyKZKZ-KBM_oILrsYmxrRvqslq1XKpPOu-PxWAqM2YQRNtIIkfGNjdLgbnyQMQwV9S6-7q1kLm1XDgsaN2EnDXkJ6_beIzmvaZhhCP2ap-DTA38tfvEYOKSqIW9TKCUO5VymWOcmRHwxudZv0puBYSLYWyXE78hr9JOViR30HfpW5R65HrJuykOAwhyHldiADUT2RykcdOeFp0lAezpnWmOT03JX13bvVnJWdhU_YQQ2__7KLVc7MA5OOsxONSVGoqS39Lm4nd96x7AJ74DiiEt0cGVNiX4hxlP4fCCwapvNDKHMb32ZqkaSzegR7EFFPFz7_EZhAyVcETb3GZmy6YMICfAqKZC4Vc5KJHy0__zp9mrRWC7zuW-wbuak0QChjvYawTNJomXT4czBd5d5WtoNiDkXYnRNbmzoTs4hNHFxrMvWKX0hg596CdRWkkqNSsy4qErQGwlnMG6u0y8vSbqkaB9N77MV7Pr9R1JUtkkgu3z_kKwtlwHkHZO6MGRYNv_EixOvcK3LKRLLdA0ukacUr-W9CuNJq3nXFkXdkhk66xSzkg7UlHH1g7H7EWTI8zlf18XmEXxmIMApPY3YhKd_2kaysWfIyfTuYN4uQ8Vo0qqwgQcDJc1rx5b_9JIMe3chVdwaZYnjv5z1cWhVBKr_9Gwmfc-SCZOBDX95jLGk82lC4GV_nAS0AZH47KfAgc-1MN4nfHZN1FZisSh74PeUCIR7JUs3jRpOBv7n47aGYHx57dAUdZPJKhVdDEs7XmCFwNj31TOS1qIzHk4oOvdh0_rk61mbL3UZ6YIGHdZc-bsC-i39HYq3YEig5qeVkXT2xjfva5FYGdR8cTtWNtCzZNQqwKAhbty2h6HhKMEz01B-CwjH9FsObHGBGSDqEwCbdPTaeaFO5E23DU45TyX-1Bu5ygVP0xAh1WVTP8zlfTePFE37ix8Fa3gkFllTdWcQtU9iSzZYgU14GeIC2m-LXwr6ixF6FbvqVL-0kn1GLxxN8AcWqawwBYdLlI_Xs6YVaR5shXhfMJI1n8yd5t2riWgQZqN2T1AeFvUGB_VDqLvujkKyZ7MxxjUgficCQocKWX3XlEnlT-yb23sBD4bgE8rTrzsX7zL0G0kf3gABn9KuYPDT-Ruu5PmBBaWBhzr_ZovccEY2IRZaNZRiRk-UJF8pP1vIMkIufXGnzEZ4xDl2HNqWdiUg-ybGvyX3NHoluVn7fAz4M1W-vRW9cbAmJj4Bom4wySGffCoDazVaGRTT4lhOEBl93RI22cpvm6yyj4FSCIFtyzIFuo-2efuBfNtdawFWuMbJO9YvRMhjZr_Vj-ZqvKhelWnMrqqS2z3kpnhMYq9bYT-XpCHwM5g0HU5bswUc2TnQ2E2Ywvl1gFNQUny6PugpkT8D2DNSb7C4R0MbyLIhAXsUC9yXTZtDFFXQy5PnyIURBkfqmN8aaMA5cBM97pscWKGAWHrR3Bi86GhIyrboJsiF1q2LSMX0G30aWBOGbDOnUnoh7FLOd6pI0c4Ku5IVyiDNapjUMt8LN6qVjrXnekTTCByCShegxkb2Z3Vt2LADMM8Dy6SbDZ7Sy0gXkXJfB_2lBN4sP_uXtUL_z0piyLokuAQ-2mNiWf3HmP8Z0JV33s5SPBxOO1RjfacpaBO0v_KTpMPebXiqKV9EiWLE7hk5ZoJX226j0UfaogIe55rT4Whl-FTWQllaW-ilD-v-p5ZKURUnpQ5elQdjRj65xq5C8FnvgpycQb7QBxd8KuhqhjBVeUIkZoBq5H2I1OVW8fVKH5tRu0Pfk21plJJC6vuBqswZUHrbIORBG1Nf7k7ZWT2IqSQJ-lqteIJutzVrsr_BAAa-4NpX09OVmT-IleAqdwM5_b4MqCH_AqxAqqnpmDfXUG32QqJMiT-TOan0mGmal_PbxxlNjhNQbn7htqMv4hGziXdlGuBPxWID_9wUbzEPfKF69TqjqrgMlHKdzloLOjjr49ZOfZs5JfmqH5pRJm4Ww6nzzb0kN9D8YWpmWJ7RzmhcGgM_2zMvxBR2eF7P4qmPNdv9pyl9bR0NhC06DTsnilG_jGowkgMSlewf3j50UXhe6JTEgWdMLi5Ca7hEqJsFp7VtYYgTmaviQc3ETW0mmH-lWTsmnXEhrV-ACHIYh1ugk8EtC29R_-BRYFFKoPzbrYfqMm5aRCTt4j330qyNqhc2l-8QYQzI9MevqqTnREM0xB9FsI8bc-EXX5-LN19xEzcFQ5Xv6P6T4XZw3ax6Vrd7I5KFrEoffJa7gTiZM-7_MqINe5rkWwClcw6i12KbMDMUmTFjCc66xWZCWA3j20bepG6I8oWu9HSBVaKBOD_VaXiScWgSwjfaI3-6IHlDkkhq61KNEAkTUGgttOJCVaxORMbPoA8ppizWqo9FUpQqRi523LqYw_Jdc5BIm8vnUKVWGOplbJN7gAj3dJ58U00Wy5H1gzIoknL538zvu4UnVMbyKa_qCYAvkRdyCbB5OwZpNEOHp0bYg8cl6UVyQKopIY-NN0C7i3HcMDils21Wj63_o1aVyv9d0iW9QwpqSpYDApECwM7TF1QP7pYChqkpf6iwxpGaDelEkNTCD_AKw98GeI4-wktXFzRzirvAZFIBkbA2G7FUxvNwPyh0F5YYGL_5OSVR4OW64-qQnR3uvhCjLCVmbhiotajRSFGak5STrmO10tAnA5KbMKElEWVEVgwKVv5GGnUY1rY_jKMlotPwiInfL_1Ugc7eKsZO0o3cJZDUR8O1Up2TlX87gFv0EsYnFP3n8EbOh3m7wEdGO9wRnltofx7Kq3vLP48VK9s_Bu08Q6whG5qba9hk0fs2J76SiQ3yK8X-NzOFGMnpfa9NfAmkLhZzi_kFQc52G7Pn52dHiTL6epPsFh3kP3Y-UsyTqjdxVcU0yl7vzR-QgqmvAdVT53KB7tdLjahK6SIxVA&response=eyJyZXNwb25zZSI6IiIsInMiOiI5NGEyIiwiZSI6ImJYVnMifQ..&t=14334&ct=14334&bg=\u0021LCqgKi8KAAQeGvVPbQEHewCIS5xitkg1V8DoKPToZW0P2MkkDqz7GFPxkI0ngm-WV0QQec03yg0VD75PcN0hx847yP6yWI8e--q-oS8nofVsFTKlHnoAoQMF6ylj2tRx_4fdQt_0LyRGGizFJBD9OZPXBmW-a9VNBLoQSHdT_F-yQOsSoDQzdT0pPv6cKfLGz1D-51EgCIV_gZwIBNGlLVv9JPuwaT0RfRsjIq2bkG3geQNByN-74pSPHm_RHtnfyPM3l55nq5WKUroPasJMbHe_2BCiZzM5ha-cCog2RjQ2J8qEjUHlpWjomI2mg3Do1XlnQ5i1avw_FRTOjLtCoc9J1pYLHZ1W_52Ixj6fADEI5ucDDOH3h4ICgvNY0zroSKDMhrqTPU10dSJwGyGuWs4wqfb7F1Ha9Xlan_nxD5h7RzJvMNjsQymQuIJOFiiuLtjQSlsSNG8oo1d57xbnVk5ZkCrV_82H9Yy_qPCwvmmVX6O8FzSkqWZSav4vBWLHnnVRd71ywQ5wZ7g_rii-L3QkFW6Ht_tV4hhsvPzpQnGN2GT3OVbF2IrHyF27jZ4sy2joQXsDzrDDLgh98QXaNxLDd5eXqQqO5wVMvzqCpz_eLDn3FDQVTjUqu4VXdVUB1hDJWJV7dMc3wqiaGngaOrU4FRBX6gzBiwRkpgEUeQih8I-imGBc9pwUNWSXy0EN47RxNBCpPjbaRfAvTRUXjCpnxiJazV8Fy48i1Abw0kMgw_dNyMJDvkoQRbZIJ6R-ZrWKmkqfbU6kjaedFEXQhhFnSgLMKQEC6GZ6Ay3tbYPvnW02xuFSWh9S5aMIriQI1L_p26RwZj9RYDEAYsiRj1AC-e2SW35FzVczb3z-hxHr_I9D9H4nBnKkt-gBZCu14_d1p0Jdk_WQ-LWleiFUGWUznmhNkdU2AXtfC3ox8DDODiMJuBi2cGbClBXW_2s3JtshuNk6YqiPx7qNisE3m7IKoGDhoXBW8y3OlW7fsT29s1UItn-SPiMkb-QKZgKXxGb3y995K7QWYkd64SN11vGL_HqaI4AdX5UdAiGDzxzsOyprQgln7T0epXhB8kXan_AehcQaCIbHTSwnfaxQ0PnYtRwdcm__lGz7814o8vnViOQnYebEJmORwEuLVIo7jL0y7T8hB86g8-WglThM3e9-apA7aM9ywfz2yC2nD4lDaNNP-a4lZVLkIigZER1yyE7m06KFoHW6O6xxh9T_kQ2Z4cKnVDjGt3Cm9RaJh2YI2T1C3jA2eGwmIvMUwREy0798SiGNBqt1uWMGnffl5nWFDxAtFiA94q_f2YcQWUkp0w8boGXeNO0ZwSny-9DQKDIBHHYbFL_8JK3eOB8LAuZoYas3UNcPk3rpidmxo6IBRP1X_zuuokLcEeimriHohJq7vsCWzNa1KgVRzvGwx-O7IplNpK0Cp0R9_WDjA1F_5RIrqpo8xwFcJs0wQW6CCEq1pXfUE6L14L1WvnyS6Qw6-9h03jUWKZ8K_u5As9MJhkrk19QVZGr63QFnWCXB8N1lUr0SGslIY-Om5LBMUe7k6t-lxnq70KPHV6Fe9uLDhUtt9oQ-lEhBSlggkuAe3IsBCuxr7ikTouCFnPh2FkCwMDk-RHSjtGjB3ZNJmos4Rm3nY5DeRhPwvRQDY9m3esVUSZlXWyhA2vGxGrvg99-j4BMYE7IYYtXI7K85ulv98AYJD88vGeoULm9bkHzEIb_fVpw7S-9TQhDuSXcHTfbYV2FIqs88whntp-ZrtFtaATyLd7ZWzNhIYT-AHEHYi3b39jwgzUl0iSbSA3OKwQaTK7nuwj1hTvgMPJOA3V9A-s4YarBsTODY5yE7DP8tJ91u1NCefmdT0HnWaibk-7Hop0EJ7vFRiiPkNDYQURqhyM50WZhi5iwYelcCooo7vV-p3qtXhyUYQxsGs6SrynzGUMKSow7ht1a3CM5AThPpBb0v-Y08SwPUgugrFKJieox55O5TEnVE2_tbeu0X68frdpVJX1ESS8E4XLUPBYyrR1exfjmtpZCzOA0WOl3MSnt0DAls3Z2X3nqtFmsaA2C_2f93divAosKTykjZFzQ9PjUUAexKFYJjheeTer5G5aaFXN7Z06zgVXrUwQgIsn2OYGKMLB8F_cjSerc5GYajHCpB9vUUnsdIDNaxigcqroneBa7E54ANZ4r4x9mmAvRkPe6K7omXBBdZ-lPWUdBqO8zY3XVTpJ7RgPiBav_oGHhS0uZYGy-aM2lsiIzTYKA_koC0XW41GgOK9Rw8KwRASHzBVS9yLbSxHxRc_mlnwAcSFXxnYx47FYLlL2NEdSGkj5swhxICDxKBrFBwtnERhEZgWkU1z0OMRXFopuwiu88CZow7Ttkzf2dtcBibQbvqhOKoGUWOoakybLud8K4c_idxO1RFgazUB3ZlGf0oKx019LYJ1EksT89weg8JydvRHqcu2GU7kV_40OyYXL_FQs6wzn2CDdJ9hGIJ_VnvOT0M2l77FpdOAP7SzTe_qIeDPUWZpK3eg1Clx12endzNehPHqjAWGlF-SbXw2j_KcnHnTzGmgp1XAzIe4ZAnpoT6b-tnUgaoA_FaJHnZwVNnziZUW2e6q1wn3yxRNilZeSeSL-BUerltNIsN_Snp9ae5dRuaCkTbclpOMOtaUQKtbLOb3O12OmQL_c8m9ohv_IzhW5Knd1W6wXI8vrcAox89ADqROVccEET3j3lEzvQg9cUr_vNugdGQtyyDVTpu7VGh8PapadrOnOjZnIGuBIoeez6bXjAcW7ga9_mM_6vKuMW-86TU-vZhuYkeXh814R-aWyQjXYXLa45cImqmnqTZ_MpUq9z6qQVS-7qgFLFG9hIIsvrv2cQwzAb8YbQMpderKoAajXrhUzEERjokjK69qklHaHKSLlV_YH6HwtLCEz98h5rDbmx4GOH9NfttrQ' ;
curl 'https://www.google.com/recaptcha/api2/clr?k=6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' \
  --data-raw $'\n(6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV\u0012ù\r03AFcWeA6fyDVyslMgq4pD6Q2rdmx1r3bppD5rG651SxXk9uHAGrFZw049VqWa3UoC22tWvFU8vxKaxrFIORPrEl0F8MQN-l0mOmiQkFEpT-puPiXhyzYEO8sXJBp_LbQLp53a36j0QBgMVhw_FBuo7PVyyhmwJvQ74SL8T2-tqcLRDrNmlkxKbZN4H25Z7a30_q5OUobJxGa3fr7PKlSnyXhVtEcDLoI5SbQjlfE7tbMDBh6zPmD0NewfzNlO3qtW4I9YsVuWE3HxMc4y1JMG6c-diYZCko9-HJZkfpic61pyX8izDdFVOq2_GviomDBilYwjWIGrMjj74O2somYrMOa16UxpLeoskfLJ1LpH0-OiBRurG0soeJbHy8vRF17b0avOxKknM7Pu7QHK34SXTGnrAuqxraXV_cizJtBzfYaTvzx_-jFmD9886VhOzCrxsjwfLU4hNSKMGFrxyZoMQL2jFLD7mbtWU6VM8urLWQZAZLe0LNm3J5nu0_OK7VsE6S8P8eUf8dCx4Xt155zYuih_1KyZGZ8_Qt6Qb-aP6L0SG6H6gDOrgz1IoCMuFBfpF9ncBQq77f0bNdcuPuMOr5-SrnEdV3b2RKQLmMQWgJAJLc-crHoEBNAMHQQh54ukfNHY7CAdbU_aDGZR5DIe7qaUJq7zyBgDeZbjxKr0YmhFPaG4DObGspsQZ_tZVFAtb19G4_hBNd-JdaqRNUukC_K_x638_tEzoytG-gKI9JjWJfyrWKL6qsn2DlqwpXHe1UKDoU60CeDkOOFOpVvvUWplw0bbXDKpo4K2pA-S3r7CoKzb7Cx1O9ZhxDjqUks9oIP0f9o2daJsOQzoNdXx9RYYLZxZM1kaaS8k7EIKDCmK342xX7qmwVwrMh3GkQLPDDJ-4DrQmVO8Cw6C9n_-TFd8qs_toRzOak9igAGeahU-p6ue3wyeHTirvMyDemGE-k2WGu_E3ihIiX9svAA36KHd31ehhYCVnP5cWIc5A1n5FgJbWJHQxkM0-5MQlyqeEbM7_TxxV26fUzMhgiNgt0sNfv4vGMyGobCQuBjJqHDeXKOr9xedLG9p10KwvPJADBYqtdJzqVu7SdFvT6KIgWTNH8l6aDV_JjaBCn6kflXUOEeL8mZ1J_FzFZQIG2PN2mDxYbRf2psEP-MDGp_ggwmEJHJsc9Ltxm4ajH31-fybpB3mNM3KJ3fYz6Ja50AMw0iSDv7sRElotj07GhumrOdqx5aTO6lTOAXKqTna_JDkhoDT5qQRbrOYrXzfa0aL9AyttOnCbFSFSwfvXfcwbzQsR4lEXS_P-K8aD1fnIJ88PKDoxAu47L9GzQzlVCB1oJXWaGcY9gEJBt0fvCvJ5gPq149HvOI5-PjhsvPZGQY5bsTOCh_23wWNF1DYyFs_VTjsnfwK3EuiBNP5B3BtmuEx5yO5Y4FaaRsOacV8jfEI6niJDGsEN92BhQzbLJ3hkICoPJj1KISCgLPnAhnygPua79fMkQO9SGt775hwC_PMTbs5SVjBCLhQUrXtwqGBJw8SXyCXhbzobZE4qH8tUpRfZyPY5XfojtUaYFua4mGL72w5zBXGsnbgNpwf_VJIxezNFWadTsOvZ_SSNwfE5wTielE8R0tN5HsSCfo_HeY-9ZS8d65f44TJDx48Znfff7prRfiZ4WPEXI4SMIROQV_cZxiRHIOW8LHFOPZuyfEgNJWoEy6RmSJko3b1KmG5YYVx8YUh1adCXq5Md91vCewpkAMAbpHhl3uHTqXyE1sfestmP-NJtmM\u001a\u0018DBIsSQ0s2djD_akThoRUDeHa"H\u0018\u0000*D\n\u0009\u0008\u0005\u0010½þ3\u0018\u008b\u0004\n\u0008\u0008\u0006\u0010É\u00854\u0018"\n\u0008\u0008\u0003\u0010©ø4\u0018\u000b\n\u0009\u0008\u0001\u0010í\u00814\u0018Ó\u0003\n\u0008\u0008\u0002\u0010Â\u00854\u0018A\n\u000e\u0008\u0004\u0010»\u00884\u0018\u0096\u0002*\u0003\u0010\u0085V' ;
curl 'https://www.google.com/recaptcha/api2/clr?k=6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' \
  --data-raw $'\n(6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV\u0012ù\r03AFcWeA6fyDVyslMgq4pD6Q2rdmx1r3bppD5rG651SxXk9uHAGrFZw049VqWa3UoC22tWvFU8vxKaxrFIORPrEl0F8MQN-l0mOmiQkFEpT-puPiXhyzYEO8sXJBp_LbQLp53a36j0QBgMVhw_FBuo7PVyyhmwJvQ74SL8T2-tqcLRDrNmlkxKbZN4H25Z7a30_q5OUobJxGa3fr7PKlSnyXhVtEcDLoI5SbQjlfE7tbMDBh6zPmD0NewfzNlO3qtW4I9YsVuWE3HxMc4y1JMG6c-diYZCko9-HJZkfpic61pyX8izDdFVOq2_GviomDBilYwjWIGrMjj74O2somYrMOa16UxpLeoskfLJ1LpH0-OiBRurG0soeJbHy8vRF17b0avOxKknM7Pu7QHK34SXTGnrAuqxraXV_cizJtBzfYaTvzx_-jFmD9886VhOzCrxsjwfLU4hNSKMGFrxyZoMQL2jFLD7mbtWU6VM8urLWQZAZLe0LNm3J5nu0_OK7VsE6S8P8eUf8dCx4Xt155zYuih_1KyZGZ8_Qt6Qb-aP6L0SG6H6gDOrgz1IoCMuFBfpF9ncBQq77f0bNdcuPuMOr5-SrnEdV3b2RKQLmMQWgJAJLc-crHoEBNAMHQQh54ukfNHY7CAdbU_aDGZR5DIe7qaUJq7zyBgDeZbjxKr0YmhFPaG4DObGspsQZ_tZVFAtb19G4_hBNd-JdaqRNUukC_K_x638_tEzoytG-gKI9JjWJfyrWKL6qsn2DlqwpXHe1UKDoU60CeDkOOFOpVvvUWplw0bbXDKpo4K2pA-S3r7CoKzb7Cx1O9ZhxDjqUks9oIP0f9o2daJsOQzoNdXx9RYYLZxZM1kaaS8k7EIKDCmK342xX7qmwVwrMh3GkQLPDDJ-4DrQmVO8Cw6C9n_-TFd8qs_toRzOak9igAGeahU-p6ue3wyeHTirvMyDemGE-k2WGu_E3ihIiX9svAA36KHd31ehhYCVnP5cWIc5A1n5FgJbWJHQxkM0-5MQlyqeEbM7_TxxV26fUzMhgiNgt0sNfv4vGMyGobCQuBjJqHDeXKOr9xedLG9p10KwvPJADBYqtdJzqVu7SdFvT6KIgWTNH8l6aDV_JjaBCn6kflXUOEeL8mZ1J_FzFZQIG2PN2mDxYbRf2psEP-MDGp_ggwmEJHJsc9Ltxm4ajH31-fybpB3mNM3KJ3fYz6Ja50AMw0iSDv7sRElotj07GhumrOdqx5aTO6lTOAXKqTna_JDkhoDT5qQRbrOYrXzfa0aL9AyttOnCbFSFSwfvXfcwbzQsR4lEXS_P-K8aD1fnIJ88PKDoxAu47L9GzQzlVCB1oJXWaGcY9gEJBt0fvCvJ5gPq149HvOI5-PjhsvPZGQY5bsTOCh_23wWNF1DYyFs_VTjsnfwK3EuiBNP5B3BtmuEx5yO5Y4FaaRsOacV8jfEI6niJDGsEN92BhQzbLJ3hkICoPJj1KISCgLPnAhnygPua79fMkQO9SGt775hwC_PMTbs5SVjBCLhQUrXtwqGBJw8SXyCXhbzobZE4qH8tUpRfZyPY5XfojtUaYFua4mGL72w5zBXGsnbgNpwf_VJIxezNFWadTsOvZ_SSNwfE5wTielE8R0tN5HsSCfo_HeY-9ZS8d65f44TJDx48Znfff7prRfiZ4WPEXI4SMIROQV_cZxiRHIOW8LHFOPZuyfEgNJWoEy6RmSJko3b1KmG5YYVx8YUh1adCXq5Md91vCewpkAMAbpHhl3uHTqXyE1sfestmP-NJtmM\u001a\u0018DBIsSQ0s2djD_akThoRUDeHa"\u0004\u0018\u0001*\u0000' ;
curl 'https://signup.snowflake.com/api/v1/createtrial' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -b 'signupsession=eyJleHBlcmltZW50U2VnbWVudHMiOnsiU0FOSVRZX0FBX1RFU1QiOjF9fQ==; signupsession.sig=QvGyu2U7LKU5ePq1KDZdVYsttXE' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'signup-flow-type: Default' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' \
  -H 'x-mutiny-experience-count: 0' \
  --data-raw '{"firstName":"dhr","lastName":"sehe","email":"<EMAIL>","company":"rhse","phoneNumber":"*********","role":"CEO","edition":"Enterprise","cloud":"gcp","region":"me-central2","country":"Japan","recaptchaToken":"03AFcWeA5rM9qJqUzp3VPwXInDE7YxieJueAjdbqMH0UVdlaE6VZSol_fhVZePD4npo-EBxSbk6ZjWHuR4UkRtJvULe6rmHGSQkkp9q1wdf0vVQvFnbiJmRDQkvRW_ceJeDfyZnyotIRbKLiMt-eFhNivDx6Tyh05aG2s_25bu8tog1MN1JFv6Fo1PCHdqtFGpIh1syQOg3RfLdHHT37k1C9hmpuKgRd46itE8yuckvHPN00VX9y9B0yO09R4Anqd0O79YK8wMvofQVocnDdPf_cqugSU4tKjV5yTPY07VQruzPKnhI2A1sDtIVd1qLwPHeC7_MeHGz5VtZSHT_eJtuegD9vc9zk5viCBuqiOIyDjOLEfHUZgMqt0Zj9o6YtB4DP2nwLrn9KQZoeaW6YYyJUjdEMDlevuZoPtc6oANC6q1xAPBnS-LUzJBx0BBSHjpVCzptNTNA7VF9DkK6e3FBr5DXoLXdrN6IYfb2OlnRE5sQGrqzptx_VR804Pq7Ie5BEPsTkFX4fd5U4cviRB96hocamPWySEqjdAQu8nlQypvMI-1NjGMsDSZ6op3L-JGEJgE4vxz_qA4QU98YHtjKnrwJ6DXtHJ46uOX3SeX1A1OKkiiIRyDi8hkUlfuRpknHXnGGVbHL_8LYbSHWHUR3fAZdTFVVHGQ6aEyRLRV42RXwv6JioZtGE7N-tcmz1B2vSG4-BQ-FuEGvhutFYaZKMtPFJ7z8U0cLqK1yCl5pcUFr4x3O6X-tAle7Q08cjoinZhAkMH_bqit9mev0SaXDHjOSimvFBNO93RX2SsPU4W5bMtGWvJy5CyLz97PQulFGpjlaua0MyzKpYIwEII33lJ1BPzR1b-rI_qqPJXG2G7Ub0Lc1DoeVdBHXiAkSS4J9TahcJF4Mhz8G02i5Qo9moqE7HUHbvHFEGncMB3ZAn5iIt6gFVIlENDap575hdyFoyF8kH1rQY5kLJfdYxDiV7__CR3uh8U-r2rKM-8D_R2tceErvJSdfqmikjUDjB85ajfY-0SFMFu8","signupUrl":"https://signup.snowflake.com/?utm_cta=trial-en-www-homepage-top-right-nav-ss-evg&amp;_ga=2.74406678.*********.1657561304-1006975775.1656432605&amp;_gac=1.*********.1656541671.Cj0KCQjw8O-VBhCpARIsACMvVLPE7vSFoPt6gqlowxPDlHT6waZ2_Kd3-4926XLVs0QvlzvTvIKg7pgaAqd2EALw_wcB#","formId":"f7469113-a110-44a5-a7ca-70c6d34b52b2","formTwoCompletionTime":0.879}' ;
curl 'https://signup.snowflake.com/static/images/snowflake-document.svg' \
  -H 'accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -b 'signupsession=eyJleHBlcmltZW50U2VnbWVudHMiOnsiU0FOSVRZX0FBX1RFU1QiOjF9fQ==; signupsession.sig=QvGyu2U7LKU5ePq1KDZdVYsttXE' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: image' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' ;
curl 'https://signup.snowflake.com/static/images/virtual-onboarding.svg' \
  -H 'accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -b 'signupsession=eyJleHBlcmltZW50U2VnbWVudHMiOnsiU0FOSVRZX0FBX1RFU1QiOjF9fQ==; signupsession.sig=QvGyu2U7LKU5ePq1KDZdVYsttXE' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: image' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' ;
curl 'https://signup.snowflake.com/static/images/solution-center.svg' \
  -H 'accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -b 'signupsession=eyJleHBlcmltZW50U2VnbWVudHMiOnsiU0FOSVRZX0FBX1RFU1QiOjF9fQ==; signupsession.sig=QvGyu2U7LKU5ePq1KDZdVYsttXE' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: image' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw $'["Check your inbox\u0021","Explore industry-specific user cases and walkthroughs in our <b10>Solutions Center</b10>","Sign up for a free, instructor-led <b10>Virtual Hands-On Lab</b10>","Get started with <b10>Snowflake documentation</b10>","Meanwhile, here are a few resources to check out:","An email to activate your account has been sent to <b10><EMAIL></b10>. It may take a few minutes to arrive."]' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'access-control-request-headers: content-type' \
  -H 'access-control-request-method: POST' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'