curl 'https://9rjj6kfe.r.ap-southeast-2.awstrack.me/L0/https:%2F%2Ffohwaju-iq91358.snowflakecomputing.com%2Fconsole%2Flogin%3FactivationToken=ver%253A1-hint%253A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%252FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%252FxqG4YG7r%252B6UAFFGDEZz%252BBalatS6KJ2UI2rmApc2u/1/010801986115ff37-2cd4743c-91fd-442d-a657-532c40eb6281-000000/3lSjlOgsw_1A2yuyyIzSIFCTAwo=218' \
  -H 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
  -H 'Accept-Language: ja' \
  -H 'Connection: keep-alive' \
  -H 'Sec-Fetch-Dest: document' \
  -H 'Sec-Fetch-Mode: navigate' \
  -H 'Sec-Fetch-Site: none' \
  -H 'Sec-Fetch-User: ?1' \
  -H 'Upgrade-Insecure-Requests: 1' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
  -H 'accept-language: ja' \
  -H 'priority: u=0, i' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: document' \
  -H 'sec-fetch-mode: navigate' \
  -H 'sec-fetch-site: none' \
  -H 'sec-fetch-user: ?1' \
  -H 'upgrade-insecure-requests: 1' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/main-0e29d6bf54e8cc5c.css' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'Referer: https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/generated-styles.1748452818751.css' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'Referer: https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/core-js-e26d99cb9e0d4cdc.js' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'Referer: https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/redux-0e2635f4f7499775.js' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'Referer: https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/react-1702d543896f3429.js' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'Referer: https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/main-d94b3cfc7a587a1b.js' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'Referer: https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/Inter-Regular-358200361080fccb.woff2' -H 'Referer;' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/Inter-Medium-94afb4f3993a225e.woff2' -H 'Referer;' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/Inter-Bold-ff699d3134c17db1.woff2' -H 'Referer;' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/770-9a9c43087557e178.js' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'Referer: https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/assets/ui/LoginUi/<EMAIL>' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'Referer: https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' ;
curl 'https://www.gstatic.com/_/translate_http/_/ss/k=translate_http.tr.pgV-E-68K-A.L.W.O/am=gMA/d=0/rs=AN8SPfpszKJssl6IA0boGClFdsaAZGtXEQ/m=el_main_css' \
  -H 'accept: text/css,*/*;q=0.1' \
  -H 'accept-language: ja' \
  -H 'priority: u=0' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: style' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-storage-access: active' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-client-data: CI22yQEIo7bJAQipncoBCKXrygEIlqHLAQiFoM0BCIn9zgEY4eLOAQ==' ;
curl 'https://translate.googleapis.com/_/translate_http/_/js/k=translate_http.tr.ja.0NONZmZ4qKA.O/am=AAAE/d=1/exm=el_conf/ed=1/rs=AN8SPfo8HnJ6yzUP-UJEuk4XXizEs8lf3Q/m=el_main' \
  -H 'accept: */*' \
  -H 'accept-language: ja' \
  -H 'origin: https://fohwaju-iq91358.snowflakecomputing.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-client-data: CI22yQEIo7bJAQipncoBCKXrygEIlqHLAQiFoM0BCIn9zgEY4eLOAQ==' ;
curl 'https://translate.google.com/gen204?nca=te_li&client=te_lib&logld=vTE_20250729' \
  -H 'accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8' \
  -H 'accept-language: ja' \
  -b '__Secure-3PSID=g.a000ywgHkAuKoS0JPqiFHfsw12m1S1mYVnyVU1gxV0o0MkS9ieH0Lvi9HpoyJbU1f1_E096rugACgYKAUUSARYSFQHGX2MiAqCsTQYv-uuzOt0S8508CxoVAUF8yKrv7kX-r3YYj3UPePNapstz0076; __Secure-3PAPISID=-gLhcX1ojwGzaZaR/Aq2RHSBFJJ2-CMapF; __Secure-3PSIDTS=sidts-CjIB5H03P2OW-EjHGQzlyU8QBK86ciLOeXNpRlJ2V2wdy7WEWvloAP09AWqOmc7tCN-ZNRAA; NID=525=fIG4A0faEs8onqL_CV396jjNamBKGTUHCj-QmkO8u14gan5D1EFufhkSZ527yQqdcS5nDYYc9B-h-O_ui2K6sFdFjKsHA4NIskiJfS8CXsMbhQm2LwwVlANuviTTThZNSJrTXP8PGBG6tbqCBDSMYBGp5wcXjlQHACAEllMqovrJyQg4VghJw3yNhSkDoWkN-edBYBj3A8faptxCgGqsW0Qbmjm0m_wwF_BgirLcHmTUiSoe-smi6UXKmTik9Pf5m8xA0wzoTe0bOsfTg40R8RDLU6jbTwkSRNDAqh_vElLfEEgp; __Secure-3PSIDCC=AKEyXzWpEdMKTZdr1T3dXk3PdHH9TiCwfTjJ8fWVkxRpne29sO3QDix4w9XO6MhaGWqep8FP' \
  -H 'priority: i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: image' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-storage-access: active' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-client-data: CI22yQEIo7bJAQipncoBCKXrygEIlqHLAQiFoM0BCIn9zgEY4eLOAQ==' ;
curl 'https://fonts.gstatic.com/s/i/productlogos/translate/v14/24px.svg' \
  -H 'accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8' \
  -H 'accept-language: ja' \
  -H 'priority: i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: image' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-storage-access: active' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-client-data: CI22yQEIo7bJAQipncoBCKXrygEIlqHLAQiFoM0BCIn9zgEY4eLOAQ==' ;
curl 'https://translate-pa.googleapis.com/v1/translateHtml' \
  -H 'accept: */*' \
  -H 'accept-language: ja' \
  -H 'content-type: application/json+protobuf' \
  -H 'origin: https://fohwaju-iq91358.snowflakecomputing.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-client-data: CI22yQEIo7bJAQipncoBCKXrygEIlqHLAQiFoM0BCIn9zgEY4eLOAQ==' \
  -H 'x-goog-api-key: AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520' \
  --data-raw '[[["Snowflakeへようこそ","<a i=0>rshe</a><a i=1>sehh</a><a i=2> 様、開始するには、ユーザー名とパスワードを選択してください</a>","ユーザー名","ユーザー名には文字と数字のみを含めることができます。","パスワード","パスワードは 14 ～ 256 文字で、少なくとも 1 つの数字、 0 文字の特殊文字、 1 文字の大文字、 1 文字の小文字が含まれている必要があります。","パスワードを確認","はじめる","English","Français","Snowflake"],"auto","zh-CN"],"te_lib"]' ;
curl 'https://translate-pa.googleapis.com/v1/translateHtml' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: ja' \
  -H 'access-control-request-headers: content-type,x-goog-api-key' \
  -H 'access-control-request-method: POST' \
  -H 'origin: https://fohwaju-iq91358.snowflakecomputing.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://translate.google.com/gen204?sl=auto&tl=zh-CN&textlen=14&ttt=1252&ttl=1041&ttf=746&sr=1&nca=te_time&client=te_lib&logld=vTE_20250729' \
  -H 'accept: image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8' \
  -H 'accept-language: ja' \
  -b '__Secure-3PSID=g.a000ywgHkAuKoS0JPqiFHfsw12m1S1mYVnyVU1gxV0o0MkS9ieH0Lvi9HpoyJbU1f1_E096rugACgYKAUUSARYSFQHGX2MiAqCsTQYv-uuzOt0S8508CxoVAUF8yKrv7kX-r3YYj3UPePNapstz0076; __Secure-3PAPISID=-gLhcX1ojwGzaZaR/Aq2RHSBFJJ2-CMapF; __Secure-3PSIDTS=sidts-CjIB5H03P2OW-EjHGQzlyU8QBK86ciLOeXNpRlJ2V2wdy7WEWvloAP09AWqOmc7tCN-ZNRAA; NID=525=fIG4A0faEs8onqL_CV396jjNamBKGTUHCj-QmkO8u14gan5D1EFufhkSZ527yQqdcS5nDYYc9B-h-O_ui2K6sFdFjKsHA4NIskiJfS8CXsMbhQm2LwwVlANuviTTThZNSJrTXP8PGBG6tbqCBDSMYBGp5wcXjlQHACAEllMqovrJyQg4VghJw3yNhSkDoWkN-edBYBj3A8faptxCgGqsW0Qbmjm0m_wwF_BgirLcHmTUiSoe-smi6UXKmTik9Pf5m8xA0wzoTe0bOsfTg40R8RDLU6jbTwkSRNDAqh_vElLfEEgp; __Secure-3PSIDCC=AKEyXzWpEdMKTZdr1T3dXk3PdHH9TiCwfTjJ8fWVkxRpne29sO3QDix4w9XO6MhaGWqep8FP' \
  -H 'priority: i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: image' \
  -H 'sec-fetch-mode: no-cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-storage-access: active' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-client-data: CI22yQEIo7bJAQipncoBCKXrygEIlqHLAQiFoM0BCIn9zgEY4eLOAQ==' ;
curl 'https://translate.googleapis.com/element/log?format=json&hasfast=true&authuser=0' \
  -H 'accept: */*' \
  -H 'accept-language: ja' \
  -H 'content-encoding: gzip' \
  -H 'content-type: application/binary' \
  -H 'origin: https://fohwaju-iq91358.snowflakecomputing.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-client-data: CI22yQEIo7bJAQipncoBCKXrygEIlqHLAQiFoM0BCIn9zgEY4eLOAQ==' \
  -H 'x-goog-authuser: 0' \
  --data-raw $'\u001f\u008b\u0008\u0000\u0000\u0000\u0000\u0000\u0000\níTËnÛ0\u0010ü\u0095bÏ\u0094²|\u0089bn\u008eú¸åT \u0007\u00920ØX±èÊ¢kSu\u0091¯/$ËMÜ¤p\u001b´\u0087\u0016\u0021\u0084\u00810\u0098%wÉÝ1\u0086\u0092®oÛ_\u0006ó\u0014\u0009+\u000fÓßû7s\u0086L¢bz¢\u008c1ð.Æe[¿ª\u009am\\×@\u0080r\u0009\u008e\u0018¸\u008e)\u009båW[ß-\u0080@9r£(ôë£Ì\u0011$ð\u0021t\u008b¸ß\r\u001cæ\u0098#\u0010øZ\u0016@à Ê1W(tNé¸¯ è\u009c#´T\u0094\u0018\u0003TI®\u0095\u0094\u009cc¡àL\u0081ðd\u0085ÿ=XXy{în^àñ<Pb\u0021Õó6|´@,P]"^1\u009d½eúu&*5Ë´@\u0091é\u008aÑJê\u0099¤8³@\u008c\u0085&¥Íîòââ66{¿ê³ðYS.Ë|×Åýmë?Õ7q½éSè\u0096ùM\\\u001fßÆX\u0018ÂÝp0ã%\u008eË=LÈBç¿\u0084¥O\u0021vcJ\r\u009b"\u000eß¨¢Z\u0017Sè_\u0021\u000e©\u008c¨\u0015§\'2­\u0084\u009c\u0008Fe\u0021\u0008£ì\u0088Î\r\u0005¤­ïv­OõåÆ/ëy\u001býb\u009eâ<t\u0021\u0005ß\u0086»z1Tôãf\u0086\u0093²Ð\'7bÜ°\u001e¶õ#»Ë8\u0013\u0088?\u007f`:xÒ½\u0083H\u0021Î;\u0088\u0085»&«®ÿØ<Yð}\u008aÿÊx¾XÉóÀ°çXÉi{³ßmoæ\u001cùÞÞ\u0082£Bpß\u0000tìrE\u0015\u0008\u0000\u0000' ;
curl 'https://translate.googleapis.com/element/log?format=json&hasfast=true&authuser=0' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: ja' \
  -H 'access-control-request-headers: content-encoding,content-type,x-goog-authuser' \
  -H 'access-control-request-method: POST' \
  -H 'origin: https://fohwaju-iq91358.snowflakecomputing.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://translate-pa.googleapis.com/v1/translateHtml' \
  -H 'accept: */*' \
  -H 'accept-language: ja' \
  -H 'content-type: application/json+protobuf' \
  -H 'origin: https://fohwaju-iq91358.snowflakecomputing.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-client-data: CI22yQEIo7bJAQipncoBCKXrygEIlqHLAQiFoM0BCIn9zgEY4eLOAQ==' \
  -H 'x-goog-api-key: AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520' \
  --data-raw '[[["パスワードが一致しません。"],"auto","zh-CN"],"te_lib"]' ;
curl 'https://fohwaju-iq91358.snowflakecomputing.com/session/v1/login-request?__uiAppName=Login' \
  -X 'POST' \
  -H 'accept: */*' \
  -H 'accept-language: ja' \
  -H 'content-length: 616' \
  -H 'content-type: application/json' \
  -H 'origin: https://fohwaju-iq91358.snowflakecomputing.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A12140812-ETMsDgAAAZhhFdjdABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEIgpDqFLXHS6AfhN43trWRAAAABQnviueByI6m3wb3kDMFCCLhSA5DLcPJdERd1jr4PXIxzZ%2FiHMvk8aeuPR2toqKY2dfj2FB3zBbcPBxaKpktaaiwYthDPJdIt%2FxqG4YG7r%2B6UAFFGDEZz%2BBalatS6KJ2UI2rmApc2u' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' ;
curl 'https://apps-api.c1.me-central2.gcp.app.snowflake.com/complete-oauth/snowflake?code=6B0B3EEB08B5B2BCD1557BEA5B9659E1B2E5DE5E&state=%7B%22url%22%3A%22https%3A%2F%2Ffohwaju-iq91358.snowflakecomputing.com%22%2C%22accountActivation%22%3Atrue%2C%22inFlightCtx%22%3A%22ver%3A1-hint%3A12140812-ETMsDgAAAZhhFqKLABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEF0c4%2BfG1z9qg1JdqegShOoAAACQILddoP4HP5UAH0%2BuqpKQE0ywU%2FCP5NoPRtEZCE8BKC%2BFxj5%2FI6GP8gviEHYKvjQlPCS4MEw5C2n%2Bni%2F32OM8hUNTxIej%2Fxf3jTWJLTThRJyEoJMx1sUVZ6vDSdVCvxYTisEWpJbOl2KNtKOVreMGt42%2FH5qwpSL77cwlqA4ofZaJGDg%2F4WqDEVHY%2F6oU4g5hABQkoDHCuoHYh%2B4Ef9PuIDAhXkFGRA%3D%3D%22%7D' \
  -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
  -H 'accept-language: ja' \
  -b 'mutiny.user.token=bdf670ec-a5fd-4cfc-9afb-b6e08af9c5c9; mutiny.user.session=279ebb52-fc45-4079-a60c-1d3ae993a30f; mutiny.user.session_number=1; OptanonAlertBoxClosed=2025-07-31T15:23:54.138Z; OptanonConsent=isGpcEnabled=0&datestamp=Fri+Aug+01+2025+00%3A23%3A54+GMT%2B0900+(%E6%97%A5%E6%9C%AC%E6%A8%99%E6%BA%96%E6%99%82)&version=202504.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=34bb4c89-cb01-4534-b3a8-d8e5fe110c17&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=1%3A1%2C2%3A1%2C3%3A1%2C4%3A1&intType=1; _gid=GA1.2.1421468475.1753975435; _hp2_id.2025084205=%7B%22userId%22%3A%22782450169767055%22%2C%22pageviewId%22%3A%22266385072222172%22%2C%22sessionId%22%3A%227492966289792289%22%2C%22identity%22%3Anull%2C%22trackerVersion%22%3A%224.0%22%7D; _gcl_au=1.1.84726269.1753975435; _uetsid=5eeee1f06e2211f0931bd99551e454a5; _uetvid=5eeeff606e2211f099432924c13e0431; _fbp=fb.1.1753975434933.201949943177310159; _mkto_trk=id:252-RFO-227&token:_mch-snowflake.com-603f41e4df9b57746b89390fe2c6063c; _hjSessionUser_1314116=eyJpZCI6ImNhZTczYzg2LWJjZWUtNWQ4ZC04NGQxLTc1NzIzZDNiNTYxYyIsImNyZWF0ZWQiOjE3NTM5NzU0MzUxODgsImV4aXN0aW5nIjpmYWxzZX0=; _hjSession_1314116=eyJpZCI6ImFlYzg0NDBhLTIyMmYtNDQ2NS1iZDdjLWRjYjg0NGNmYjJhMSIsImMiOjE3NTM5NzU0MzUxODksInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MH0=; _clck=3yj3tl%7C2%7Cfy2%7C0%7C2038; _hp2_ses_props.2025084205=%7B%22ts%22%3A1753975434722%2C%22d%22%3A%22signup.snowflake.com%22%2C%22h%22%3A%22%2F%22%7D; __adroll_fpc=44697487ab52b8edcc9675b74ca14efd-1753975436633; _ga=GA1.1.1426234456.1753975435; _ga_KXJB3GGT1Y=GS2.1.s1753975475$o1$g0$t1753975475$j60$l0$h0; _clsk=13g11d8%7C1753975475289%7C2%7C1%7Cn.clarity.ms%2Fcollect' \
  -H 'priority: u=0, i' \
  -H 'referer: https://fohwaju-iq91358.snowflakecomputing.com/' \
  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: document' \
  -H 'sec-fetch-mode: navigate' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-fetch-user: ?1' \
  -H 'upgrade-insecure-requests: 1' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'